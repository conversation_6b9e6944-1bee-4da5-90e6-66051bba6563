import os
from dotenv import load_dotenv
from alpaca.trading.client import TradingClient

load_dotenv()
api_key = os.getenv('ALPACA_API_KEY')
secret_key = os.getenv('ALPACA_SECRET_KEY')
paper = os.getenv('ALPACA_PAPER_TRADE', 'True').lower() == 'true'

print(f'API Key: {api_key[:8]}...')
print(f'Paper Trading: {paper}')

try:
    client = TradingClient(api_key, secret_key, paper=paper)
    account = client.get_account()
    print(f'✅ Successfully connected to Alpaca API!')
    print(f'Account Status: {account.status}')
    print(f'Buying Power: ${account.buying_power}')
    print(f'Portfolio Value: ${account.portfolio_value}')
except Exception as e:
    print(f'❌ Error connecting to Alpaca API: {e}')
